import { createContext, useContext, useState, useEffect } from "react";
import { isAuthenticated, logoutUser } from "../Utils/AuthApi";
import apiClient from "../Utils/AxiosConfig";

const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
	const [isLoggedIn, setIsLoggedIn] = useState(false);
	const [user, setUser] = useState(null);
	const [isLoading, setIsLoading] = useState(true);
	const [isSidebarOpen, setIsSidebarOpen] = useState(false);
	const [isDesktop, setIsDesktop] = useState(window.innerWidth >= 1024);
	const [syncStatusChecked, setSyncStatusChecked] = useState(false);

	// Check authentication status on app load
	useEffect(() => {
		const checkAuthStatus = async () => {
			try {
				setIsLoading(true);
				console.log("Checking authentication status on app load..."); // Debug log
				const authResult = await isAuthenticated();
				console.log("Authentication result:", authResult); // Debug log

				if (authResult.success) {
					console.log("User is authenticated, setting logged in state"); // Debug log
					setIsLoggedIn(true);
					setUser(authResult.user);
				} else {
					console.log("User is not authenticated, setting logged out state"); // Debug log
					setIsLoggedIn(false);
					setUser(null);
				}
			} catch (error) {
				console.error("Auth check error:", error);
				setIsLoggedIn(false);
				setUser(null);
			} finally {
				setIsLoading(false);
			}
		};

		checkAuthStatus();
	}, []);

	// Function to check sync status after login
	const checkSyncStatusAfterLogin = async () => {
		try {
			console.log("Checking sync status after login...");
			const response = await apiClient.get("/sync-status");
			const syncStatus = response.data.data.syncStatus;

			if (syncStatus) {
				console.log("Sync in progress detected after login");
				// Set sync in progress in localStorage so Header can pick it up
				localStorage.setItem('syncInProgress', 'true');
			}
			setSyncStatusChecked(true);
		} catch (error) {
			console.error("Failed to check sync status after login:", error);
			setSyncStatusChecked(true); // Mark as checked even if failed
		}
	};

	const login = (userData) => {
		setIsLoggedIn(true);
		setUser(userData);
		// Check sync status after successful login
		checkSyncStatusAfterLogin();
	};

	const logout = async () => {
		try {
			await logoutUser();
		} catch (error) {
			console.error("Logout error:", error);
		} finally {
			setIsLoggedIn(false);
			setUser(null);
			// Backend handles cookie cleanup automatically
		}
	};

	const toggleSidebar = () => setIsSidebarOpen((prev) => !prev);

	// Update isDesktop when the window resizes
	useEffect(() => {
		const handleResize = () => {
			setIsDesktop(window.innerWidth >= 1024);
		};

		window.addEventListener("resize", handleResize);
		return () => window.removeEventListener("resize", handleResize);
	}, []);

	return (
		<AuthContext.Provider value={{
			isLoggedIn,
			user,
			isLoading,
			login,
			logout,
			isSidebarOpen,
			toggleSidebar,
			isDesktop,
			syncStatusChecked
		}}>
			{children}
		</AuthContext.Provider>
	);
};

export const useAuth = () => useContext(AuthContext);
